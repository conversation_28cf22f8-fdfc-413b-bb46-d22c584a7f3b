# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AG-UI is a lightweight, event-based protocol for agent-human interaction. This is a monorepo containing:

- **TypeScript SDK** (`typescript-sdk/`): Core implementation with packages, integrations, and the AG-UI Dojo demo app
- **Python SDK** (`python-sdk/`): Python implementation of the AG-UI protocol
- **Documentation** (`docs/`): Mintlify-based documentation site

## Architecture

### Core Components

- **Protocol Implementation**: Event-based system using RxJS (TypeScript) and Pydantic (Python)
- **Middleware Layer**: Flexible compatibility layer for various event transports
- **Framework Integrations**: Support for LangGraph, CrewAI, Mastra, LlamaIndex, and more
- **Reference Implementation**: HTTP-based connector for quick setup

### Key Directories

- `typescript-sdk/packages/core/`: Core protocol implementation
- `typescript-sdk/packages/client/`: Client libraries
- `typescript-sdk/packages/encoder/`: Message encoding/decoding
- `typescript-sdk/integrations/`: Framework-specific implementations
- `typescript-sdk/apps/dojo/`: Interactive demo showcasing AG-UI capabilities

## Development Commands

### TypeScript SDK

```bash
# From typescript-sdk/ directory
pnpm install           # Install dependencies
pnpm build             # Build all packages
pnpm dev               # Start development mode
pnpm lint              # Run linting
pnpm check-types       # Type checking
pnpm test              # Run tests
```

### Python SDK

```bash
# From python-sdk/ directory
poetry install         # Install dependencies
poetry build           # Build package
poetry run pytest     # Run tests
```

### 📚 Documentation & Explainability
- `docs/`: Core protocol concept and implementation
- `ag_ui_intergration/`: AG-UI protocol implementation details
- `ag_ui_intergration/ag-ui_implementation_document/`: AG-UI protocol implementation details
- `ag_ui_intergration/ag_ui_document/`: AG-UI protocol implementation details

### Testing

- **TypeScript**: Uses Jest, run `pnpm test` in specific package directories
- **Python**: Uses pytest, run `poetry run pytest` in python-sdk/
- Tests are located in `__tests__` directories alongside implementation files

## Build System

- **Turborepo**: Orchestrates builds across the monorepo
- **PNPM**: Package management with workspaces
- **TypeScript**: Shared tsconfig.json configurations
- **Poetry**: Python package management

## Integration Development

When creating new framework integrations:

1. Use existing integrations as templates (`typescript-sdk/integrations/`)
2. Each integration should have its own package.json and tsconfig.json
3. Include Jest configuration for testing
4. Follow the established event emission patterns from core packages

## Event System

AG-UI uses ~16 standard event types for agent-human interaction:
- Real-time streaming events
- Bi-directional state synchronization
- Generative UI events
- Human-in-the-loop collaboration events

Refer to `typescript-sdk/packages/core/src/` for event type definitions and implementation patterns.

### 🧠 AI Behavior Rules
- **Never assume missing context. Ask questions if uncertain.**
- **Never hallucinate libraries or functions** – only use known, verified Python packages.
- **Always confirm file paths and module names** exist before referencing them in code or tests.
- **Never delete or overwrite existing code** unless explicitly instructed to or if part of a task from `TASK.md`.