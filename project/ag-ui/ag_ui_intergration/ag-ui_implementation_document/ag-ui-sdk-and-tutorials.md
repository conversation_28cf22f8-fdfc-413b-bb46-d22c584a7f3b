# AG-UI SDK and Tutorials Documentation

## State Management Events

These events are used to manage and synchronize the agent's state with the frontend. State management in the protocol follows an efficient snapshot-delta pattern where complete state snapshots are sent initially or infrequently, while incremental updates (deltas) are used for ongoing changes. This approach optimizes for both completeness and efficiency: snapshots ensure the frontend has the full state context, while deltas minimize data transfer for frequent updates. Together, they enable frontends to maintain an accurate representation of agent state without unnecessary data transmission.

```mermaid
sequenceDiagram
participant Agent
participant Client
Note over Agent,Client: Initial state transfer
Agent->>Client: StateSnapshot
Note over Agent,Client: Incremental updates
loop State changes over time
Agent->>Client: StateDelta
Agent->>Client: StateDelta
end
Note over Agent,Client: Occasional full refresh
Agent->>Client: StateSnapshot
loop More incremental updates
Agent->>Client: StateDelta
end
Note over Agent,Client: Message history update
Agent->>Client: MessagesSnapshot
```

The combination of snapshots and deltas allows frontends to efficiently track changes to agent state while ensuring consistency. Snapshots serve as synchronization points that reset the state to a known baseline, while deltas provide lightweight updates between snapshots.

### StateSnapshot

Provides a complete snapshot of an agent's state. The `StateSnapshot` event delivers a comprehensive representation of the agent's current state. This event is typically sent at the beginning of an interaction or when synchronization is needed. It contains all state variables relevant to the frontend, allowing it to completely rebuild its internal representation. Frontends should replace their existing state model with the contents of this snapshot rather than trying to merge it with previous state.

| Property | Description |
| ---------- | ----------------------- |
| `snapshot` | Complete state snapshot |

### StateDelta

Provides a partial update to an agent's state using JSON Patch. The `StateDelta` event contains incremental updates to the agent's state in the form of JSON Patch operations (as defined in RFC 6902). Each delta represents specific changes to apply to the current state model. This approach is bandwidth-efficient, sending only what has changed rather than the entire state. Frontends should apply these patches in sequence to maintain an accurate state representation. If a frontend detects inconsistencies after applying patches, it may request a fresh `StateSnapshot`.

| Property | Description |
| -------- | ----------------------------------------- |
| `delta` | Array of JSON Patch operations (RFC 6902) |

### MessagesSnapshot

Provides a snapshot of all messages in a conversation. The `MessagesSnapshot` event delivers a complete history of messages in the current conversation. Unlike the general state snapshot, this focuses specifically on the conversation transcript. This event is useful for initializing the chat history, synchronizing after connection interruptions, or providing a comprehensive view when a user joins an ongoing conversation.

| Property | Description |
| ---------- | ------------------------ |
| `messages` | Array of message objects |

## Special Events

### RawEvent

Used to pass through events from external systems. The `RawEvent` allows agents to forward events from other systems without modification, preserving the original event structure while wrapping it in the AG-UI protocol format.

| Property | Description |
| -------- | ------------------- |
| `event` | Original event data |
| `source` | Source of the event |

### CustomEvent

Used for application-specific custom events. The `CustomEvent` provides a flexible mechanism for applications to define their own event types while remaining compatible with the AG-UI protocol.

| Property | Description |
| -------- | ------------------------------- |
| `name` | Name of the custom event |
| `value` | Value associated with the event |

---

# Core Types Documentation

## RunAgentInput

Input parameters for running an agent. In the HTTP API, this is the body of the `POST` request.

```typescript
type RunAgentInput = {
  threadId: string
  runId: string
  state: any
  messages: Message[]
  tools: Tool[]
  context: Context[]
  forwardedProps: any
}
```

| Property | Type | Description |
| ---------------- | ----------- | ---------------------------------------------- |
| `threadId` | `string` | ID of the conversation thread |
| `runId` | `string` | ID of the current run |
| `state` | `any` | Current state of the agent |
| `messages` | `Message[]` | Array of messages in the conversation |
| `tools` | `Tool[]` | Array of tools available to the agent |
| `context` | `Context[]` | Array of context objects provided to the agent |
| `forwardedProps` | `any` | Additional properties forwarded to the agent |

## Message Types

### Role

Represents the possible roles a message sender can have.

```typescript
type Role = "developer" | "system" | "assistant" | "user" | "tool"
```

### DeveloperMessage

Represents a message from a developer.

```typescript
type DeveloperMessage = {
  id: string
  role: "developer"
  content: string
  name?: string
}
```

### SystemMessage

Represents a system message.

```typescript
type SystemMessage = {
  id: string
  role: "system"
  content: string
  name?: string
}
```

### AssistantMessage

Represents a message from an assistant.

```typescript
type AssistantMessage = {
  id: string
  role: "assistant"
  content?: string
  name?: string
  toolCalls?: ToolCall[]
}
```

### UserMessage

Represents a message from a user.

```typescript
type UserMessage = {
  id: string
  role: "user"
  content: string
  name?: string
}
```

### ToolMessage

Represents a message from a tool.

```typescript
type ToolMessage = {
  id: string
  content: string
  role: "tool"
  toolCallId: string
}
```

### ToolCall

Represents a tool call made by an agent.

```typescript
type ToolCall = {
  id: string
  type: "function"
  function: FunctionCall
}
```

#### FunctionCall

Represents function name and arguments in a tool call.

```typescript
type FunctionCall = {
  name: string
  arguments: string
}
```

## Context

Represents a piece of contextual information provided to an agent.

```typescript
type Context = {
  description: string
  value: string
}
```

## Tool

Defines a tool that can be called by an agent.

```typescript
type Tool = {
  name: string
  description: string
  parameters: any // JSON Schema
}
```

## State

Represents the state of an agent during execution.

```typescript
type State = any
```

The state type is flexible and can hold any data structure needed by the agent implementation.

---

# Python SDK Documentation

## Installation

```bash
pip install ag-ui-protocol
```

## Core Events (Python)

```python
from ag_ui.core import EventType

class EventType(str, Enum):
    TEXT_MESSAGE_START = "TEXT_MESSAGE_START"
    TEXT_MESSAGE_CONTENT = "TEXT_MESSAGE_CONTENT"
    TEXT_MESSAGE_END = "TEXT_MESSAGE_END"
    TOOL_CALL_START = "TOOL_CALL_START"
    TOOL_CALL_ARGS = "TOOL_CALL_ARGS"
    TOOL_CALL_END = "TOOL_CALL_END"
    TOOL_CALL_RESULT = "TOOL_CALL_RESULT"
    STATE_SNAPSHOT = "STATE_SNAPSHOT"
    STATE_DELTA = "STATE_DELTA"
    MESSAGES_SNAPSHOT = "MESSAGES_SNAPSHOT"
    RAW = "RAW"
    CUSTOM = "CUSTOM"
    RUN_STARTED = "RUN_STARTED"
    RUN_FINISHED = "RUN_FINISHED"
    RUN_ERROR = "RUN_ERROR"
    STEP_STARTED = "STEP_STARTED"
    STEP_FINISHED = "STEP_FINISHED"
```

## BaseEvent (Python)

```python
from ag_ui.core import BaseEvent

class BaseEvent(ConfiguredBaseModel):
    type: EventType
    timestamp: Optional[int] = None
    raw_event: Optional[Any] = None
```

## Event Encoder

```python
from ag_ui.encoder import EventEncoder

# Initialize the encoder
encoder = EventEncoder()

# Encode an event
encoded_event = encoder.encode(event)
```

---

# Tutorials

## Developing with Cursor

Use Cursor to build AG-UI implementations faster

### Adding the documentation to Cursor

1. Open up the Cursor settings
2. Go to Features > Docs and click "+ Add new Doc"
3. Paste in the following URL: https://docs.ag-ui.com/llms-full.txt
4. Click "Add"

### Using the documentation

Now you can use the documentation to help you build your AG-UI project. Load the docs into the current prompt by typing the `@` symbol, selecting "Docs" and then selecting "Agent User Interaction Protocol" from the list.

### Best practices

When building AG-UI servers with Cursor:

* Break down complex problems into smaller steps
* Have a look at what the agent was doing by checking which files it edited (above the chat input)
* Let the agent write unit tests to verify your implementation
* Follow AG-UI protocol specifications carefully

## Debugging

### The AG-UI Dojo

The AG-UI Dojo is the best way to bring AG-UI to a new surface, and is also an excellent resource for learning about the protocol's basic capabilities. It provides a structured environment where you can test and validate each component of the AG-UI protocol.

#### What is the Dojo?

The Dojo consists of a series of "hello world"-sized demonstrations for the different building blocks available via AG-UI. Each demonstration:

1. Shows a specific AG-UI capability in action
2. Presents both the user-visible interaction and the underlying code side by side
3. Allows you to test and verify your implementation

#### Using the Dojo as an Implementation Checklist

When working on bringing AG-UI to a new surface or platform, you can use the Dojo as a comprehensive "todo list":

1. Work through each demonstration one by one
2. Implement and test each AG-UI building block in your environment
3. When all demonstrations work correctly in your implementation, you can be confident that full-featured copilots and agent-native applications can be built on your new surface

#### Common Debugging Patterns

When using the Dojo for debugging your AG-UI implementation, keep these patterns in mind:

1. **Event Sequence Issues**: Verify that events are being emitted in the correct order and with proper nesting (e.g., `TEXT_MESSAGE_START` before `TEXT_MESSAGE_CONTENT`)
2. **Data Format Problems**: Ensure your event payloads match the expected structure for each event type
3. **Transport Layer Debugging**: Check that your chosen transport mechanism (SSE, WebSockets, etc.) is correctly delivering events
4. **State Synchronization**: Confirm that state updates are correctly applied using snapshots and deltas
5. **Tool Execution**: Verify that tool calls and responses are properly formatted and processed

#### Getting Started with the Dojo

To start using the AG-UI Dojo:

1. Clone the repository from [github.com/ag-ui-protocol/dojo](https://github.com/ag-ui-protocol/dojo)
2. Follow the setup instructions in the README
3. Start working through the demonstrations in order
4. Use the provided test cases to validate your implementation

The Dojo's structured approach makes it an invaluable resource for both learning AG-UI and ensuring your implementation meets all requirements.
